# 🍽️ GrubSniff - AI-Powered Menu Protein Analyzer

GrubSniff is a React application that uses OCR (Optical Character Recognition) and AI to analyze menu images and identify the menu items with the highest protein content, complete with estimated macronutrients.

## Features

- 📸 **Image Upload**: Drag-and-drop or click to upload menu images
- 🔍 **OCR Text Extraction**: Uses Tesseract.js to extract text from menu images
- 🤖 **AI Analysis**: Leverages Google's Gemini API to analyze menu items for protein content
- 📊 **Macro Breakdown**: Displays estimated protein, carbs, fat, and calories
- 🔬 **Detailed Nutrition**: Get comprehensive nutritional information including fiber, sodium, and health scores
- 📱 **Responsive Design**: Works on desktop and mobile devices

## Prerequisites

- Node.js (v16 or higher)
- A Google Gemini API key (get one from [Google AI Studio](https://makersuite.google.com/app/apikey))

## Installation

1. Clone the repository:

```bash
git clone <repository-url>
cd grubsniff-frontend
```

2. Install dependencies:

```bash
npm install
```

3. Start the development server:

```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## Usage

1. **Enter API Key**: On first use, enter your Gemini API key (it will be saved locally)
2. **Upload Menu**: Drag and drop or click to upload a clear image of a menu
3. **Wait for Processing**: The app will extract text using OCR and analyze it with AI
4. **View Results**: See the recommended high-protein item with estimated macros
5. **Get Details**: Click for detailed nutritional information

## API Key Setup

You'll need a Google Gemini API key to use this application:

1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key
3. Enter it in the app when prompted

The API key is stored locally in your browser and never sent to any third-party servers.

## Technologies Used

- **React 19** - Frontend framework
- **Vite** - Build tool and development server
- **Tailwind CSS** - Styling framework
- **Tesseract.js** - OCR for text extraction
- **Google Gemini API** - AI analysis for menu items
- **JavaScript/ES6+** - Programming language

## Project Structure

```
src/
├── components/
│   ├── ImageUpload.jsx      # Image upload component with drag-and-drop
│   └── ResultsDisplay.jsx   # Results and nutrition display
├── services/
│   ├── ocrService.js        # Tesseract.js OCR integration
│   └── geminiService.js     # Google Gemini API integration
├── App.jsx                  # Main application component
├── App.css                  # Custom styles
└── main.jsx                 # Application entry point
```

## Tips for Best Results

- Use clear, well-lit images of menus
- Ensure text is readable and not blurry
- Avoid images with heavy shadows or glare
- Portrait orientation often works better than landscape
- Make sure the menu text is in English

## Troubleshooting

**OCR not working well?**

- Try a clearer image with better lighting
- Ensure the text is large enough and not blurry
- Avoid images with complex backgrounds

**API errors?**

- Check that your Gemini API key is valid
- Ensure you have internet connectivity
- Verify the API key has proper permissions

**No results showing?**

- Make sure the extracted text contains actual menu items
- Check the browser console for any error messages

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Disclaimer

Nutritional estimates are AI-generated approximations based on typical restaurant serving sizes and should not be used for medical or dietary planning without consulting a healthcare professional.
