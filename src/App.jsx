import { useState, useEffect } from 'react';
import ImageUpload from './components/ImageUpload';
import ResultsDisplay from './components/ResultsDisplay';
import ocrService from './services/ocrService';
import geminiService from './services/geminiService';
import './App.css';

function App() {
  const [apiKey, setApiKey] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [ocrProgress, setOcrProgress] = useState(0);
  const [extractedText, setExtractedText] = useState('');
  const [analysisResult, setAnalysisResult] = useState(null);
  const [detailedNutrition, setDetailedNutrition] = useState(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState('upload'); // upload, ocr, analysis, results

  // Load API key from localStorage on mount
  useEffect(() => {
    const savedApiKey = localStorage.getItem('gemini-api-key');
    if (savedApiKey) {
      setApiKey(savedApiKey);
      try {
        geminiService.initialize(savedApiKey);
      } catch (err) {
        console.error('Failed to initialize Gemini service:', err);
      }
    }
  }, []);

  const handleApiKeySubmit = (e) => {
    e.preventDefault();
    if (!apiKey.trim()) {
      setError('Please enter a valid API key');
      return;
    }

    try {
      geminiService.initialize(apiKey);
      localStorage.setItem('gemini-api-key', apiKey);
      setError('');
    } catch (err) {
      setError('Failed to initialize Gemini service: ' + err.message);
    }
  };

  const handleImageUpload = async (imageFile) => {
    if (!apiKey) {
      setError('Please enter your Gemini API key first');
      return;
    }

    setIsProcessing(true);
    setError('');
    setCurrentStep('ocr');
    setOcrProgress(0);
    setExtractedText('');
    setAnalysisResult(null);
    setDetailedNutrition(null);

    try {
      // Step 1: Extract text using OCR
      const ocrResult = await ocrService.extractTextFromImage(
        imageFile,
        (progress) => setOcrProgress(progress)
      );

      if (!ocrResult.success) {
        throw new Error(ocrResult.error);
      }

      const cleanedText = ocrService.preprocessText(ocrResult.text);
      setExtractedText(cleanedText);

      if (!cleanedText || cleanedText.length < 10) {
        throw new Error('Could not extract meaningful text from the image. Please try a clearer image.');
      }

      // Step 2: Analyze with Gemini
      setCurrentStep('analysis');
      const analysisResult = await geminiService.analyzeMenuForProtein(cleanedText);

      if (!analysisResult.success) {
        throw new Error(analysisResult.error);
      }

      setAnalysisResult(analysisResult.data);
      setCurrentStep('results');

    } catch (err) {
      setError(err.message);
      setCurrentStep('upload');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleGetDetailedNutrition = async (itemName, itemDescription) => {
    setIsLoadingDetails(true);
    try {
      const result = await geminiService.getDetailedNutrition(itemName, itemDescription);
      if (result.success) {
        setDetailedNutrition(result.data);
      } else {
        console.error('Failed to get detailed nutrition:', result.error);
      }
    } catch (err) {
      console.error('Error getting detailed nutrition:', err);
    } finally {
      setIsLoadingDetails(false);
    }
  };

  const resetApp = () => {
    setCurrentStep('upload');
    setExtractedText('');
    setAnalysisResult(null);
    setDetailedNutrition(null);
    setError('');
    setOcrProgress(0);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">🍽️ GrubSniff</h1>
          <p className="text-lg text-gray-600">Find the highest protein menu items with AI-powered analysis</p>
        </div>

        {/* API Key Input */}
        {!apiKey && (
          <div className="max-w-md mx-auto mb-8 p-6 bg-white rounded-lg shadow-sm border border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Enter Gemini API Key</h2>
            <form onSubmit={handleApiKeySubmit} className="space-y-4">
              <div>
                <input
                  type="password"
                  value={apiKey}
                  onChange={(e) => setApiKey(e.target.value)}
                  placeholder="Enter your Gemini API key"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Get your API key from{' '}
                  <a href="https://makersuite.google.com/app/apikey" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                    Google AI Studio
                  </a>
                </p>
              </div>
              <button
                type="submit"
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                Save API Key
              </button>
            </form>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <div className="max-w-2xl mx-auto mb-6 p-4 bg-red-50 border border-red-200 rounded-md">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Progress Indicator */}
        {isProcessing && (
          <div className="max-w-2xl mx-auto mb-6">
            <div className="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">
                  {currentStep === 'ocr' ? 'Extracting text from image...' : 'Analyzing menu with AI...'}
                </span>
                {currentStep === 'ocr' && (
                  <span className="text-sm text-gray-500">{ocrProgress}%</span>
                )}
              </div>
              {currentStep === 'ocr' && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${ocrProgress}%` }}
                  ></div>
                </div>
              )}
              {currentStep === 'analysis' && (
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full animate-pulse w-full"></div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Main Content */}
        {apiKey && (
          <>
            {currentStep === 'upload' && (
              <ImageUpload
                onImageUpload={handleImageUpload}
                isProcessing={isProcessing}
              />
            )}

            {currentStep === 'results' && analysisResult && (
              <div className="space-y-6">
                <ResultsDisplay
                  analysisResult={analysisResult}
                  onGetDetailedNutrition={handleGetDetailedNutrition}
                  detailedNutrition={detailedNutrition}
                  isLoadingDetails={isLoadingDetails}
                />

                <div className="text-center">
                  <button
                    onClick={resetApp}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Analyze Another Menu
                  </button>
                </div>
              </div>
            )}

            {/* Debug: Show extracted text */}
            {extractedText && currentStep === 'results' && (
              <div className="max-w-2xl mx-auto mt-8 p-4 bg-gray-100 rounded-lg">
                <details>
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 mb-2">
                    View Extracted Text (Debug)
                  </summary>
                  <pre className="text-xs text-gray-600 whitespace-pre-wrap">{extractedText}</pre>
                </details>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
}

export default App;
