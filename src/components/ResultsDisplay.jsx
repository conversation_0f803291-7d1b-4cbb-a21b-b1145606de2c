import { useState } from 'react';

const ResultsDisplay = ({ analysisResult, onGetDetailedNutrition, detailedNutrition, isLoadingDetails }) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!analysisResult) return null;

  const { recommendedItem, estimatedMacros, confidence, alternatives } = analysisResult;

  const handleGetDetails = () => {
    setShowDetails(true);
    if (onGetDetailedNutrition && !detailedNutrition) {
      onGetDetailedNutrition(recommendedItem.name, recommendedItem.description);
    }
  };

  const MacroCard = ({ label, value, unit, color = "blue" }) => (
    <div className={`bg-${color}-50 border border-${color}-200 rounded-lg p-4 text-center`}>
      <div className={`text-2xl font-bold text-${color}-600`}>{value}</div>
      <div className={`text-sm text-${color}-500`}>{unit}</div>
      <div className="text-xs text-gray-600 mt-1">{label}</div>
    </div>
  );

  const ConfidenceBadge = ({ level }) => {
    const colors = {
      high: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-red-100 text-red-800'
    };
    
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[level] || colors.medium}`}>
        {level} confidence
      </span>
    );
  };

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      {/* Main Recommendation */}
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
        <div className="flex items-start justify-between mb-4">
          <h2 className="text-2xl font-bold text-gray-900">🥩 Highest Protein Recommendation</h2>
          <ConfidenceBadge level={confidence} />
        </div>
        
        <div className="space-y-4">
          <div>
            <h3 className="text-xl font-semibold text-gray-800">{recommendedItem.name}</h3>
            <p className="text-gray-600 mt-1">{recommendedItem.description}</p>
          </div>
          
          <div className="bg-blue-50 border-l-4 border-blue-400 p-4">
            <p className="text-sm text-blue-700">
              <strong>Why this item:</strong> {recommendedItem.reasoning}
            </p>
          </div>
        </div>
      </div>

      {/* Macros Display */}
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Estimated Macronutrients</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <MacroCard label="Protein" value={estimatedMacros.protein} unit="g" color="green" />
          <MacroCard label="Carbs" value={estimatedMacros.carbs} unit="g" color="orange" />
          <MacroCard label="Fat" value={estimatedMacros.fat} unit="g" color="purple" />
          <MacroCard label="Calories" value={estimatedMacros.calories} unit="kcal" color="blue" />
        </div>
        
        {!showDetails && (
          <div className="mt-4 text-center">
            <button
              onClick={handleGetDetails}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Get Detailed Nutrition Info
            </button>
          </div>
        )}
      </div>

      {/* Detailed Nutrition (if requested) */}
      {showDetails && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Detailed Nutrition Information</h3>
          
          {isLoadingDetails ? (
            <div className="text-center py-8">
              <div className="inline-flex items-center px-4 py-2 font-semibold leading-6 text-sm shadow rounded-md text-blue-500 bg-blue-100">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Loading detailed nutrition...
              </div>
            </div>
          ) : detailedNutrition ? (
            <div className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{detailedNutrition.detailedMacros.fiber}g</div>
                  <div className="text-xs text-gray-600">Fiber</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{detailedNutrition.detailedMacros.sugar}g</div>
                  <div className="text-xs text-gray-600">Sugar</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{detailedNutrition.detailedMacros.sodium}mg</div>
                  <div className="text-xs text-gray-600">Sodium</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded">
                  <div className="font-semibold">{detailedNutrition.healthScore}/10</div>
                  <div className="text-xs text-gray-600">Health Score</div>
                </div>
              </div>
              
              {detailedNutrition.proteinSources && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Protein Sources:</h4>
                  <div className="flex flex-wrap gap-2">
                    {detailedNutrition.proteinSources.map((source, index) => (
                      <span key={index} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {source}
                      </span>
                    ))}
                  </div>
                </div>
              )}
              
              {detailedNutrition.dietaryInfo && (
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Dietary Information:</h4>
                  <div className="flex flex-wrap gap-2">
                    {Object.entries(detailedNutrition.dietaryInfo).map(([key, value]) => (
                      value && (
                        <span key={key} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                        </span>
                      )
                    ))}
                  </div>
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500 text-center py-4">Failed to load detailed nutrition information.</p>
          )}
        </div>
      )}

      {/* Alternative Options */}
      {alternatives && alternatives.length > 0 && (
        <div className="bg-white border border-gray-200 rounded-lg shadow-sm p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Other High-Protein Options</h3>
          <div className="space-y-3">
            {alternatives.map((alt, index) => (
              <div key={index} className="border-l-4 border-gray-300 pl-4">
                <h4 className="font-medium text-gray-800">{alt.name}</h4>
                <p className="text-sm text-gray-600">{alt.reasoning}</p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default ResultsDisplay;
