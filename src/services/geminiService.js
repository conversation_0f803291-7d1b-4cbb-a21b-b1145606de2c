import { GoogleGenerativeAI } from '@google/generative-ai';

class GeminiService {
  constructor() {
    this.genAI = null;
    this.model = null;
  }

  initialize(apiKey) {
    if (!apiKey) {
      throw new Error('Gemini API key is required');
    }
    
    this.genAI = new GoogleGenerativeAI(apiKey);
    this.model = this.genAI.getGenerativeModel({ model: "gemini-pro" });
  }

  async analyzeMenuForProtein(menuText) {
    if (!this.model) {
      throw new Error('Gemini service not initialized. Please provide an API key.');
    }

    const prompt = `
Analyze the following menu text and identify the menu item that likely has the highest protein content. 
Please provide your response in the following JSON format:

{
  "recommendedItem": {
    "name": "Name of the menu item",
    "description": "Brief description from the menu",
    "reasoning": "Why this item likely has the highest protein"
  },
  "estimatedMacros": {
    "protein": "estimated protein in grams",
    "carbs": "estimated carbohydrates in grams", 
    "fat": "estimated fat in grams",
    "calories": "estimated total calories"
  },
  "confidence": "high/medium/low - your confidence in this analysis",
  "alternatives": [
    {
      "name": "Alternative high-protein option",
      "reasoning": "Why this is also a good protein source"
    }
  ]
}

Menu text:
${menuText}

Please analyze the menu items and provide nutritional estimates based on typical serving sizes for restaurant meals. Focus on identifying items with high protein content such as meat dishes, fish, eggs, dairy, legumes, etc.
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      // Try to parse JSON from the response
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const analysisData = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          data: analysisData,
          error: null
        };
      } else {
        // If no JSON found, return the raw text
        return {
          success: false,
          data: null,
          error: 'Could not parse analysis results. Raw response: ' + text
        };
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      return {
        success: false,
        data: null,
        error: error.message || 'Failed to analyze menu with Gemini API'
      };
    }
  }

  async getDetailedNutrition(itemName, itemDescription) {
    if (!this.model) {
      throw new Error('Gemini service not initialized. Please provide an API key.');
    }

    const prompt = `
Provide detailed nutritional information for the following menu item:
Item: ${itemName}
Description: ${itemDescription}

Please respond with a JSON object containing:
{
  "detailedMacros": {
    "protein": "grams",
    "carbs": "grams",
    "fat": "grams",
    "fiber": "grams",
    "sugar": "grams",
    "sodium": "mg",
    "calories": "total calories"
  },
  "proteinSources": ["list of protein sources in this dish"],
  "healthScore": "1-10 rating for overall healthiness",
  "dietaryInfo": {
    "vegetarian": true/false,
    "vegan": true/false,
    "glutenFree": true/false,
    "dairyFree": true/false
  }
}

Base your estimates on typical restaurant serving sizes and standard nutritional values.
`;

    try {
      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();
      
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const nutritionData = JSON.parse(jsonMatch[0]);
        return {
          success: true,
          data: nutritionData,
          error: null
        };
      } else {
        return {
          success: false,
          data: null,
          error: 'Could not parse detailed nutrition data'
        };
      }
    } catch (error) {
      console.error('Gemini API Error:', error);
      return {
        success: false,
        data: null,
        error: error.message || 'Failed to get detailed nutrition information'
      };
    }
  }
}

// Create a singleton instance
const geminiService = new GeminiService();

export default geminiService;
